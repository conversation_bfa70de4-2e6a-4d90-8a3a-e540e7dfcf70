<template>
  <div class="h-screen w-screen">
    <RouterView />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import useGlobal from './store/module/useGlobal';
import { setTitle } from './utils';

const globalStore = useGlobal();
const router = useRouter();
router.beforeEach(() => {
  if (globalStore.showCompleteInfo) {
    setTitle(globalStore.oldTitle);
    globalStore.showCompleteInfo = false;
    globalStore.oldTitle = '';

    return false;
  } else {
    return true;
  }
});
defineOptions({
  name: 'APP',
});
</script>
