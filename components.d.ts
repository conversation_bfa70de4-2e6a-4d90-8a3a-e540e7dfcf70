/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    VanActionSheet: typeof import('vant/es')['ActionSheet']
    VanArea: typeof import('vant/es')['Area']
    VanButton: typeof import('vant/es')['Button']
    VanCalendar: typeof import('vant/es')['Calendar']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanCollapse: typeof import('vant/es')['Collapse']
    VanCollapseItem: typeof import('vant/es')['CollapseItem']
    VanDialog: typeof import('vant/es')['Dialog']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanIcon: typeof import('vant/es')['Icon']
    VanPicker: typeof import('vant/es')['Picker']
    VanPickerGroup: typeof import('vant/es')['PickerGroup']
    VanPopup: typeof import('vant/es')['Popup']
    VanProgress: typeof import('vant/es')['Progress']
    VanRadio: typeof import('vant/es')['Radio']
    VanRadioGroup: typeof import('vant/es')['RadioGroup']
    VanSwipe: typeof import('vant/es')['Swipe']
    VanSwipeItem: typeof import('vant/es')['SwipeItem']
  }
}
